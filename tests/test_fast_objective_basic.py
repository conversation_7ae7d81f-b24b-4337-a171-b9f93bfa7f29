"""Базовые тесты для FastWalkForwardObjective с ускорениями."""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import patch, MagicMock

from src.optimiser.fast_objective import FastWalkForwardObjective
from src.coint2.utils.config import load_config


class TestFastObjectiveBasic:
    """Базовые тесты для FastWalkForwardObjective."""
    
    def test_initialization_with_cache(self):
        """Тест инициализации с кэшем."""
        # Создаем минимальную конфигурацию
        config = load_config("configs/main_2024.yaml")
        search_space = {
            'rolling_window': {'type': 'int', 'low': 20, 'high': 50},
            'zscore_threshold': {'type': 'float', 'low': 1.5, 'high': 3.0}
        }
        
        # Мокаем инициализацию глобального кэша, чтобы избежать загрузки данных
        with patch.object(FastWalkForwardObjective, '_initialize_global_rolling_cache', return_value=True):
            objective = FastWalkForwardObjective(config, search_space)
            
            # Проверяем, что кэш инициализирован
            assert hasattr(objective, 'pair_selection_cache'), "Должен быть кэш отбора пар"
            assert isinstance(objective.pair_selection_cache, dict), "Кэш должен быть словарем"
    
    def test_convert_hours_to_periods(self):
        """Тест конвертации часов в периоды."""
        config = load_config("configs/main_2024.yaml")
        search_space = {'rolling_window': {'type': 'int', 'low': 20, 'high': 50}}
        
        with patch.object(FastWalkForwardObjective, '_initialize_global_rolling_cache', return_value=True):
            objective = FastWalkForwardObjective(config, search_space)
            
            # Тестируем конвертацию
            periods = objective.convert_hours_to_periods(4.0, 15)  # 4 часа при 15-минутных барах
            assert periods == 16, f"4 часа = 16 периодов по 15 минут, получили {periods}"
            
            periods = objective.convert_hours_to_periods(1.5, 15)  # 1.5 часа
            assert periods == 6, f"1.5 часа = 6 периодов по 15 минут, получили {periods}"
            
            periods = objective.convert_hours_to_periods(0.25, 15)  # 15 минут
            assert periods == 1, f"0.25 часа = 1 период по 15 минут, получили {periods}"
    
    def test_optimized_backtester_import(self):
        """Тест импорта оптимизированного бэктестера."""
        # Проверяем, что OptimizedPairBacktester импортируется корректно
        from src.optimiser.fast_objective import PairBacktester
        from src.coint2.engine.optimized_backtest_engine import OptimizedPairBacktester
        
        # Проверяем, что PairBacktester теперь указывает на OptimizedPairBacktester
        assert PairBacktester is OptimizedPairBacktester, "PairBacktester должен быть OptimizedPairBacktester"
    
    def test_cache_key_generation(self):
        """Тест генерации ключей кэша."""
        config = load_config("configs/main_2024.yaml")
        search_space = {'rolling_window': {'type': 'int', 'low': 20, 'high': 50}}
        
        with patch.object(FastWalkForwardObjective, '_initialize_global_rolling_cache', return_value=True):
            objective = FastWalkForwardObjective(config, search_space)
            
            # Тестируем формат ключей кэша
            start_date = pd.Timestamp('2024-01-01')
            end_date = pd.Timestamp('2024-01-31')
            
            expected_key = "2024-01-01_2024-01-31"
            actual_key = f"{start_date.strftime('%Y-%m-%d')}_{end_date.strftime('%Y-%m-%d')}"
            
            assert actual_key == expected_key, f"Ключ кэша должен быть {expected_key}, получили {actual_key}"
