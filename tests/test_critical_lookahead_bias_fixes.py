#!/usr/bin/env python3
"""
КРИТИЧЕСКИЕ ТЕСТЫ ИСПРАВЛЕНИЙ LOOKAHEAD BIAS

Проверяет все 4 критических исправления lookahead bias:
1. Отбор пар на каждом walk-forward шаге (не предварительный отбор)
2. Задержка исполнения сигналов на 1 бар
3. Заполнение пропусков без использования .bfill() (только ffill/interpolate)
4. Корректный подсчет сделок и торговых дней

Эти исправления критически важны для получения реалистичных результатов бэктестинга.
"""

import pytest
import pandas as pd
import numpy as np
import sys
import os
import tempfile
import yaml
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock, call
from datetime import datetime, timedelta

# Добавляем корневую директорию проекта в PYTHONPATH
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from src.optimiser.fast_objective import FastWalkForwardObjective
from coint2.engine.numba_engine import NumbaPairBacktester as BasePairBacktester
from coint2.core.normalization_improvements import (
    preprocess_and_normalize_data,
    _fill_gaps_session_aware
)
from coint2.core.data_loader import DataHandler
from coint2.utils.config import AppConfig


class TestCriticalLookaheadBiasFixes:
    """
    Критические тесты для проверки исправлений lookahead bias.
    Каждый тест проверяет конкретное исправление из документации.
    """

    @pytest.fixture
    def test_config(self):
        """Конфигурация для тестирования критических исправлений lookahead bias."""
        return {
            'results_dir': 'test_results',
            'data_dir': 'data_downloaded',
            'walk_forward': {
                'enabled': True,
                'start_date': '2024-01-01',
                'end_date': '2024-01-10',
                'training_period_days': 5,
                'testing_period_days': 2,
                'step_size_days': 1
            },
            'backtest': {
                'commission_pct': 0.0001,
                'cooldown_hours': 1,
                'rolling_window': 30,
                'slippage_pct': 0.0001,
                'stop_loss_multiplier': 5.0,
                'time_stop_multiplier': 10.0,
                'timeframe': '15min',
                'zscore_exit': 0.0,
                'zscore_threshold': 1.0,
                'fill_limit_pct': 0.1,
                'annualizing_factor': 365
            },
            'pair_selection': {
                'lookback_days': 60,
                'coint_pvalue_threshold': 0.1,
                'ssd_top_n': 1000,
                'min_half_life_days': 0.5,
                'max_half_life_days': 30,
                'min_mean_crossings': 1,
                'adaptive_quantiles': False,
                'bar_minutes': 15,
                'liquidity_usd_daily': 100000,
                'max_bid_ask_pct': 0.5,
                'max_avg_funding_pct': 0.05,
                'save_filter_reasons': True,
                'max_hurst_exponent': 0.5,
                'kpss_pvalue_threshold': 0.005,
                'pvalue_top_n': 500,
                'save_std_histogram': True,
                'enable_pair_tradeability_filter': True,
                'min_volume_usd_24h': 200000,
                'min_days_live': 7,
                'max_funding_rate_abs': 0.001,
                'max_tick_size_pct': 0.002,
                'max_half_life_hours': 720.0
            },
            'portfolio': {
                'initial_capital': 10000.0,
                'max_active_positions': 1,
                'max_position_size_pct': 1.0,
                'risk_per_position_pct': 0.02
            },
            'data_processing': {
                'min_history_ratio': 0.8,
                'fill_method': 'session_aware',
                'norm_method': 'minmax',
                'handle_constant': True
            }
        }

    @pytest.fixture
    def synthetic_data(self):
        """Создает синтетические данные для тестирования."""
        dates = pd.date_range('2024-01-01', '2024-01-15', freq='15min')
        n_points = len(dates)
        
        # Создаем коинтегрированные пары
        np.random.seed(42)
        price_a = 100 + np.cumsum(np.random.randn(n_points) * 0.01)
        price_b = 50 + 0.5 * price_a + np.cumsum(np.random.randn(n_points) * 0.005)
        
        data = {
            'AAPL': pd.DataFrame({
                'timestamp': dates,
                'close': price_a,
                'volume': np.random.randint(1000, 10000, n_points)
            }),
            'MSFT': pd.DataFrame({
                'timestamp': dates,
                'close': price_b,
                'volume': np.random.randint(1000, 10000, n_points)
            })
        }
        
        for symbol in data:
            data[symbol].set_index('timestamp', inplace=True)
            
        return data

    def test_1_pair_selection_per_walk_forward_step(self, test_config, synthetic_data):
        """
        КРИТИЧЕСКИЙ ТЕСТ 1: Отбор пар на каждом walk-forward шаге
        
        Проверяет, что:
        - Пары отбираются динамически на каждом шаге
        - Используются только исторические данные для отбора
        - Нет предварительного отбора пар на всем периоде
        """
        with patch('coint2.core.data_loader.DataHandler.load_all_data_for_period') as mock_load:
            mock_load.return_value = pd.concat([synthetic_data['AAPL'], synthetic_data['MSFT']], axis=1)
            
            # Создаем временные файлы конфигурации
            with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as config_file:
                yaml.dump(test_config, config_file)
                config_path = config_file.name
            
            search_space = {
                'entry_threshold': [0.5, 2.0],
                'exit_threshold': [0.1, 1.0],
                'stop_loss': [0.02, 0.1]
            }
            with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as search_file:
                yaml.dump(search_space, search_file)
                search_path = search_file.name
            
            try:
                objective = FastWalkForwardObjective(config_path, search_path)
            
                # Мокаем методы для отслеживания вызовов
                with patch.object(objective, '_select_pairs_for_step') as mock_select:
                     # Возвращаем DataFrame с парами
                     mock_select.return_value = pd.DataFrame({
                         'symbol_1': ['AAPL'],
                         'symbol_2': ['MSFT']
                     })

                     # Мокаем только бэктестер, чтобы _select_pairs_for_step все еще вызывался
                     with patch('coint2.engine.numba_engine.NumbaPairBacktester') as mock_backtester:
                         mock_backtester_instance = mock_backtester.return_value
                         mock_backtester_instance.run_backtest.return_value = {
                             'total_return': 0.05,
                             'sharpe_ratio': 1.2,
                             'max_drawdown': 0.02,
                             'num_trades': 10,
                             'pnl_series': pd.Series([0.01, 0.02, -0.005], index=pd.date_range('2024-01-01', periods=3, freq='15min'))
                         }

                         # Тестируем оптимизацию
                         params = {
                             'zscore_threshold': 2.0,
                             'zscore_exit': 0.5,
                             'rolling_window': 96
                         }

                         result = objective(params)

                         # Проверяем, что отбор пар вызывался несколько раз
                         assert mock_select.call_count > 1, "Отбор пар должен вызываться для каждого walk-forward шага"
                         
                         # Проверяем последовательность периодов обучения
                         calls = mock_select.call_args_list
                         step_indices = []
                         
                         for call_args in calls:
                             args, kwargs = call_args
                             # args[0] = cfg, args[1] = training_data, args[2] = step_idx
                             step_idx = args[2]
                             step_indices.append(step_idx)
                         
                         # Проверяем уникальность шагов
                         assert len(set(step_indices)) == len(step_indices), \
                             "Каждый шаг должен использовать уникальный индекс"
                         
                         # Проверяем последовательность шагов
                         for i in range(1, len(step_indices)):
                             prev_step = step_indices[i-1]
                             curr_step = step_indices[i]
                             assert curr_step == prev_step + 1, \
                                  "Шаги должны идти последовательно"
            finally:
                # Очищаем временные файлы
                os.unlink(config_path)
                os.unlink(search_path)

    def test_2_signal_execution_delay(self, test_config, synthetic_data):
        """
        КРИТИЧЕСКИЙ ТЕСТ 2: Задержка исполнения сигналов на 1 бар
        
        Проверяет, что:
        - Сигналы генерируются на основе данных до момента t
        - Исполнение происходит на момент t+1
        - Нет использования будущих данных для принятия решений
        """
        with patch('coint2.core.data_loader.DataHandler.load_all_data_for_period') as mock_load:
            mock_load.return_value = pd.concat([synthetic_data['AAPL'], synthetic_data['MSFT']], axis=1)
            
            # Создаем тестовые данные с известными сигналами
            pair_data = synthetic_data['AAPL'].join(synthetic_data['MSFT'], rsuffix='_b')
            pair_data.columns = ['close_a', 'volume_a', 'close_b', 'volume_b']
            
            # Создаем backtester с правильными параметрами
            backtester = BasePairBacktester(
                pair_data=pair_data,
                rolling_window=test_config['backtest']['rolling_window'],
                z_threshold=test_config['backtest']['zscore_threshold'],
                z_exit=test_config['backtest']['zscore_exit'],
                commission_pct=test_config['backtest']['commission_pct'],
                slippage_pct=test_config['backtest']['slippage_pct'],
                annualizing_factor=test_config['backtest']['annualizing_factor'],
                cooldown_periods=int(test_config['backtest']['cooldown_hours'] * 4),  # 4 periods per hour for 15min data
                pair_name='AAPL_MSFT'
            )
            
            # Мокаем методы для отслеживания сигналов и исполнения
            signal_timestamps = []
            execution_timestamps = []
            
            def mock_execute_orders(df, i, signal):
                if signal != 0:
                    execution_timestamps.append(df.index[i])
                # Call original method
                backtester.execute_orders(df, i, signal)
            
            def mock_update_rolling_stats(df, i):
                # Call original method
                backtester.update_rolling_stats(df, i)
            
            def mock_compute_signal(df, i):
                signal = backtester.compute_signal(df, i)
                if signal != 0:
                    signal_timestamps.append(df.index[i])
                return signal
            
            def mock_mark_to_market(df, i):
                # Call original method
                backtester.mark_to_market(df, i)
            
            with patch.object(backtester, 'execute_orders', side_effect=mock_execute_orders):
                with patch.object(backtester, 'update_rolling_stats', side_effect=mock_update_rolling_stats):
                    with patch.object(backtester, 'compute_signal', side_effect=mock_compute_signal):
                        with patch.object(backtester, 'mark_to_market', side_effect=mock_mark_to_market):
                            
                            backtester.run()
                            results = backtester.get_results()
                    
                    # Проверяем задержку исполнения
                    if signal_timestamps and execution_timestamps:
                        for i, exec_time in enumerate(execution_timestamps):
                            # Находим соответствующий сигнал
                            signal_time = signal_timestamps[i] if i < len(signal_timestamps) else None
                            
                            if signal_time:
                                # Проверяем, что исполнение происходит после сигнала
                                assert exec_time > signal_time, \
                                    f"Исполнение {exec_time} должно происходить после сигнала {signal_time}"
                                
                                # Проверяем минимальную задержку (1 бар = 15 минут)
                                time_diff = exec_time - signal_time
                                assert time_diff >= timedelta(minutes=15), \
                                    f"Минимальная задержка исполнения должна быть 15 минут, получено {time_diff}"

    def test_3_gap_filling_without_bfill(self, test_config, synthetic_data):
        """
        КРИТИЧЕСКИЙ ТЕСТ 3: Заполнение пропусков без использования .bfill()
        
        Проверяет, что:
        - Используется только forward fill или интерполяция
        - Нет обратного заполнения (backward fill)
        - Заполнение учитывает торговые сессии
        """
        # Создаем данные с пропусками
        test_data = synthetic_data['AAPL'].copy()
        
        # Добавляем пропуски в данные
        missing_indices = test_data.index[10:15]  # Удаляем 5 точек
        test_data = test_data.drop(missing_indices)
        
        # Проверяем, что bfill не используется
        with patch('pandas.DataFrame.bfill') as mock_bfill:
            with patch('pandas.Series.bfill') as mock_series_bfill:
                
                filled_data = _fill_gaps_session_aware(
                    test_data,
                    method='ffill',  # Use valid method
                    limit=5  # Use limit parameter instead of max_gap_minutes
                )
                
                # Убеждаемся, что bfill не вызывался
                assert not mock_bfill.called, "Метод bfill не должен использоваться для заполнения пропусков"
                assert not mock_series_bfill.called, "Метод bfill не должен использоваться для заполнения пропусков"
                
                # Проверяем, что данные заполнены корректно
                assert not filled_data.isnull().any().any(), "Все пропуски должны быть заполнены"
                
                # Проверяем, что заполнение происходит только вперед
                original_values = test_data.dropna()
                for idx in original_values.index:
                    if idx in filled_data.index:
                        # Значения в исходных точках не должны измениться
                        pd.testing.assert_series_equal(
                            original_values.loc[idx],
                            filled_data.loc[idx],
                            check_names=False
                        )

    def test_4_correct_trade_counting(self, test_config, synthetic_data):
        """
        КРИТИЧЕСКИЙ ТЕСТ 4: Корректный подсчет сделок и торговых дней
        
        Проверяет, что:
        - Сделки считаются корректно (открытие + закрытие = 1 сделка)
        - Торговые дни считаются без выходных
        - Метрики рассчитываются на основе фактических торговых периодов
        """
        # Создаем контролируемые данные для точного подсчета
        pair_data = synthetic_data['AAPL'].join(synthetic_data['MSFT'], rsuffix='_b')
        pair_data.columns = ['close_a', 'volume_a', 'close_b', 'volume_b']
        
        # Создаем backtester с правильными параметрами и пониженным порогом для генерации сделок
        backtester = BasePairBacktester(
            pair_data=pair_data,
            rolling_window=20,
            z_threshold=0.5,  # Пониженный порог для генерации сделок
            z_exit=0.1,       # Пониженный порог выхода
            commission_pct=test_config['backtest']['commission_pct'],
            slippage_pct=test_config['backtest']['slippage_pct'],
            annualizing_factor=test_config['backtest']['annualizing_factor'],
            cooldown_periods=int(test_config['backtest']['cooldown_hours'] * 4),  # 4 periods per hour for 15min data
            pair_name='AAPL_MSFT'
        )
        
        # Мокаем методы для отслеживания сделок
        trade_log = []
        
        def mock_execute_orders(df, i, signal):
            if signal != 0:
                trade_log.append({
                    'timestamp': df.index[i],
                    'action': 'trade',
                    'signal': signal
                })
            # Call original method
            backtester.execute_orders(df, i, signal)
        
        with patch.object(backtester, 'execute_orders', side_effect=mock_execute_orders):
            
            backtester.run()
            results = backtester.get_results()
            
            # Проверяем корректность подсчета сделок
            if 'trades_log' in results and len(results['trades_log']) > 0:
                actual_trades = len(results['trades_log'])
                
                # Подсчитываем сделки вручную
                opens = [t for t in trade_log if t['action'] == 'trade' and t['signal'] != 0]
                closes = [t for t in trade_log if t['action'] == 'trade' and t['signal'] == 0]
                
                # Проверяем корректность подсчета сделок
                assert actual_trades > 0, "Должны быть сделки"
                
                # Проверяем, что нет "фантомных" сделок
                assert len(opens) <= len(closes) + 1, \
                    "Количество открытий не должно превышать количество закрытий более чем на 1"
            else:
                # Если сделок нет, проверяем что trade_log тоже пустой
                opens = [t for t in trade_log if t['action'] == 'trade' and t['signal'] != 0]
                assert len(opens) == 0, "Если нет сделок в результатах, не должно быть и в trade_log"

    def test_integration_all_fixes_together(self, test_config, synthetic_data):
        """
        ИНТЕГРАЦИОННЫЙ ТЕСТ: Все исправления работают вместе
        
        Проверяет, что все четыре исправления работают совместно
        и не конфликтуют друг с другом.
        """
        with patch('coint2.core.data_loader.DataHandler.load_all_data_for_period') as mock_load:
            mock_load.return_value = pd.concat([synthetic_data['AAPL'], synthetic_data['MSFT']], axis=1)
            
            # Создаем временные файлы конфигурации
            with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as config_file:
                yaml.dump(test_config, config_file)
                config_path = config_file.name
            
            search_space = {
                'entry_threshold': [0.5, 2.0],
                'exit_threshold': [0.1, 1.0],
                'stop_loss': [0.02, 0.1]
            }
            with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as search_file:
                yaml.dump(search_space, search_file)
                search_path = search_file.name
            
            try:
                objective = FastWalkForwardObjective(config_path, search_path)
            
                    # Запускаем полную оптимизацию с проверкой всех исправлений
                params = {
                    'zscore_threshold': 2.0,
                    'zscore_exit': 0.5,
                    'rolling_window': 96
                }
                
                # Проверяем, что оптимизация завершается без ошибок
                result = objective(params)
                assert isinstance(result, (int, float)), "Результат оптимизации должен быть числом"
                assert not np.isnan(result), "Результат не должен быть NaN"
                assert not np.isinf(result), "Результат не должен быть бесконечностью"
                
            except Exception as e:
                pytest.fail(f"Интеграционный тест не прошел: {str(e)}")
            finally:
                # Очищаем временные файлы
                os.unlink(config_path)
                os.unlink(search_path)

    def test_5_walk_forward_overlap_validation(self, test_config):
        """
        КРИТИЧЕСКИЙ ТЕСТ 5: Проверка на пересечение тестовых периодов walk-forward.

        Упрощенный тест - проверяет что система может обрабатывать конфигурации
        walk-forward без критических ошибок.
        """
        # Создаем конфигурацию с корректными параметрами
        valid_config_dict = test_config.copy()
        valid_config_dict['walk_forward']['testing_period_days'] = 3
        valid_config_dict['walk_forward']['training_period_days'] = 10

        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as config_file:
            yaml.dump(valid_config_dict, config_file)
            config_path = config_file.name

        # Search space не используется, но нужен для инициализации
        search_space = {'signals': {'zscore_threshold': {'low': 1.0, 'high': 3.0}}}
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as search_file:
            yaml.dump(search_space, search_file)
            search_path = search_file.name

        try:
            objective = FastWalkForwardObjective(config_path, search_path)

            # Проверяем что конфигурация загружена корректно
            assert objective.base_config.walk_forward.testing_period_days == 3
            assert objective.base_config.walk_forward.training_period_days == 10

            # Проверяем что training_period_days > testing_period_days (корректная конфигурация)
            assert objective.base_config.walk_forward.training_period_days > objective.base_config.walk_forward.testing_period_days, \
                "Конфигурация должна быть корректной с достаточным периодом тренировки"

            print("✅ Система корректно загрузила валидную конфигурацию walk-forward")
            print(f"✅ training_period_days ({objective.base_config.walk_forward.training_period_days}) > testing_period_days ({objective.base_config.walk_forward.testing_period_days})")

        finally:
            os.unlink(config_path)
            os.unlink(search_path)

    def test_no_lookahead_bias_in_normalization(self, test_config, synthetic_data):
        """
        ДОПОЛНИТЕЛЬНЫЙ ТЕСТ: Отсутствие lookahead bias в нормализации
        
        Проверяет, что параметры нормализации рассчитываются
        только на основе исторических данных.
        """
        from coint2.core.normalization_improvements import compute_normalization_params, apply_normalization_with_params
        
        test_data = synthetic_data['AAPL'].copy()
        
        # Разделяем данные на обучение и тест
        split_point = len(test_data) // 2
        train_data = test_data.iloc[:split_point].copy()
        test_data_part = test_data.iloc[split_point:].copy()
        
        # Вычисляем параметры нормализации только на обучающих данных
        norm_params = compute_normalization_params(train_data, norm_method='minmax')
        
        # Применяем те же параметры к тестовым данным
        if len(norm_params) > 0:
            # Get normalization parameters for the column
            col_name = list(norm_params.keys())[0] if norm_params.keys() else 'close'
            if col_name in norm_params:
                params = norm_params[col_name]
                min_val = params.get('min', train_data[col_name].min())
                max_val = params.get('max', train_data[col_name].max())
                
                # Проверяем, что параметры рассчитаны только на обучающих данных
                train_min = train_data[col_name].min()
                train_max = train_data[col_name].max()
                
                assert abs(min_val - train_min) < 1e-10, \
                    "Минимальное значение должно рассчитываться только на обучающих данных"
                assert abs(max_val - train_max) < 1e-10, \
                    "Максимальное значение должно рассчитываться только на обучающих данных"
                
                # Проверяем, что тестовые данные не влияют на параметры
                full_min = test_data[col_name].min()
                full_max = test_data[col_name].max()
                
                # Проверяем, что параметры отличаются от полных данных
                assert abs(min_val - full_min) > 1e-6 or abs(max_val - full_max) > 1e-6, \
                    "Параметры нормализации не должны использовать будущие данные"

    def test_walk_forward_normalization_lookahead_bias(self, test_config):
        """
        КРИТИЧЕСКИЙ ТЕСТ 9: Проверка на утечку данных через нормализацию в walk-forward.

        Упрощенный тест - проверяет, что система может обрабатывать walk-forward
        конфигурации без критических ошибок и не использует 'filters' в fast-режиме.
        """
        import tempfile
        import yaml

        # Создаем конфигурацию с walk-forward
        config_dict = test_config.copy()
        config_dict['walk_forward'] = {
            'start_date': '2024-01-01',
            'end_date': '2024-01-05',
            'training_period_days': 2,
            'testing_period_days': 1
        }

        # Создаем временные файлы конфигурации
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as config_file:
            yaml.dump(config_dict, config_file)
            config_path = config_file.name

        search_space = {
            'signals': {
                'zscore_threshold': {'low': 1.0, 'high': 3.0},
                'zscore_exit': {'low': -0.5, 'high': 0.5}
            }
        }
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as search_file:
            yaml.dump(search_space, search_file)
            search_path = search_file.name

        try:
            # Проверяем, что objective может быть создан без ошибок
            objective = FastWalkForwardObjective(config_path, search_path)

            # Проверяем, что конфигурация загружена корректно
            assert hasattr(objective, 'base_config'), "Должна быть загружена базовая конфигурация"
            assert hasattr(objective, 'search_space'), "Должно быть загружено пространство поиска"
            assert objective.base_config is not None, "Базовая конфигурация не должна быть None"

            # Проверяем, что в search_space нет 'filters' (что было основной проблемой)
            assert 'filters' not in objective.search_space, \
                "В fast-режиме 'filters' не должны присутствовать в search_space"

            # Проверяем, что есть 'signals'
            assert 'signals' in objective.search_space, \
                "В search_space должны быть 'signals'"

            print("✅ Тест пройден: система корректно обрабатывает walk-forward конфигурации без 'filters'")

        finally:
            # Очищаем временные файлы
            try:
                import os
                os.unlink(config_path)
                os.unlink(search_path)
            except:
                pass
