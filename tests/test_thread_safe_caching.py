"""Тесты потокобезопасности кэширования в FastWalkForwardObjective."""

import sys
from pathlib import Path
import pytest
import pandas as pd
import numpy as np
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from unittest.mock import patch, MagicMock

# Добавляем корневую директорию проекта в PYTHONPATH
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.optimiser.fast_objective import FastWalkForwardObjective
from src.coint2.utils.config import load_config


class TestThreadSafeCaching:
    """Тесты потокобезопасности кэширования отбора пар."""
    
    def test_thread_safe_pair_selection_cache(self):
        """
        Тест потокобезопасности кэша отбора пар.
        Проверяет, что дорогая операция _select_pairs_for_step выполняется
        только один раз, даже при параллельных запросах.
        """
        try:
            config = load_config("configs/main_2024.yaml")
        except Exception as e:
            pytest.skip(f"Не удалось загрузить конфигурацию: {e}")
            
        search_space = {
            'rolling_window': {'type': 'int', 'low': 20, 'high': 50},
            'zscore_threshold': {'type': 'float', 'low': 1.5, 'high': 3.0}
        }
        
        # Мокаем инициализацию глобального кэша
        with patch.object(FastWalkForwardObjective, '_initialize_global_rolling_cache', return_value=True):
            try:
                objective = FastWalkForwardObjective(config, search_space)
            except Exception as e:
                pytest.skip(f"Ошибка инициализации: {e}")
        
        # Создаем тестовые данные
        test_step_data = {
            'training_start': pd.Timestamp('2024-01-01'),
            'training_end': pd.Timestamp('2024-01-31'),
            'testing_start': pd.Timestamp('2024-02-01'),
            'testing_end': pd.Timestamp('2024-02-07'),
            'training_data': pd.DataFrame({
                'BTCUSDT': np.random.randn(100),
                'ETHUSDT': np.random.randn(100)
            }),
            'full_data': pd.DataFrame({
                'BTCUSDT': np.random.randn(150),
                'ETHUSDT': np.random.randn(150)
            })
        }
        
        # Мокаем дорогую операцию _select_pairs_for_step
        mock_pairs = [{'s1': 'BTCUSDT', 's2': 'ETHUSDT', 'beta': 1.0, 'mean': 0.0, 'std': 1.0}]
        
        with patch.object(objective, '_select_pairs_for_step', return_value=mock_pairs) as mock_select:
            # Добавляем небольшую задержку в мок, чтобы симулировать дорогую операцию
            def slow_select_pairs(*args, **kwargs):
                time.sleep(0.1)  # 100ms задержка
                return mock_pairs
            
            mock_select.side_effect = slow_select_pairs
            
            # Функция для выполнения в потоке
            def process_step():
                return objective._process_single_walk_forward_step(config, test_step_data, 0)
            
            # Запускаем 5 потоков параллельно с одинаковыми данными
            num_threads = 5
            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                futures = [executor.submit(process_step) for _ in range(num_threads)]
                results = [future.result() for future in futures]
            
            # Проверяем, что все потоки получили результат
            assert len(results) == num_threads, f"Ожидали {num_threads} результатов, получили {len(results)}"
            
            # КРИТИЧЕСКАЯ ПРОВЕРКА: дорогая операция должна быть выполнена только один раз
            assert mock_select.call_count == 1, (
                f"_select_pairs_for_step должен быть вызван только 1 раз, "
                f"но был вызван {mock_select.call_count} раз. "
                f"Это означает, что кэширование не работает потокобезопасно!"
            )
            
            print("✅ Потокобезопасное кэширование работает корректно")
            print(f"   - Запущено потоков: {num_threads}")
            print(f"   - Вызовов _select_pairs_for_step: {mock_select.call_count}")
            print(f"   - Все потоки получили результат: {len(results) == num_threads}")
    
    def test_cache_lock_initialization(self):
        """Тест инициализации блокировки кэша."""
        try:
            config = load_config("configs/main_2024.yaml")
        except Exception as e:
            pytest.skip(f"Не удалось загрузить конфигурацию: {e}")
            
        search_space = {'rolling_window': {'type': 'int', 'low': 20, 'high': 50}}
        
        with patch.object(FastWalkForwardObjective, '_initialize_global_rolling_cache', return_value=True):
            try:
                objective = FastWalkForwardObjective(config, search_space)
                
                # Проверяем, что блокировка инициализирована
                assert hasattr(objective, '_cache_lock'), "Должна быть блокировка _cache_lock"
                assert isinstance(objective._cache_lock, threading.Lock), "Блокировка должна быть threading.Lock"
                
                print("✅ Блокировка кэша инициализирована корректно")
            except Exception as e:
                pytest.skip(f"Ошибка инициализации: {e}")
    
    def test_cache_key_consistency(self):
        """Тест консистентности ключей кэша."""
        # Проверяем, что одинаковые даты дают одинаковые ключи
        start_date = pd.Timestamp('2024-01-01')
        end_date = pd.Timestamp('2024-01-31')
        
        key1 = f"{start_date.strftime('%Y-%m-%d')}_{end_date.strftime('%Y-%m-%d')}"
        key2 = f"{start_date.strftime('%Y-%m-%d')}_{end_date.strftime('%Y-%m-%d')}"
        
        assert key1 == key2, "Ключи кэша должны быть консистентными"
        assert key1 == "2024-01-01_2024-01-31", f"Ожидали '2024-01-01_2024-01-31', получили '{key1}'"
        
        print("✅ Ключи кэша генерируются консистентно")


if __name__ == "__main__":
    test = TestThreadSafeCaching()
    test.test_cache_lock_initialization()
    test.test_cache_key_consistency()
    test.test_thread_safe_pair_selection_cache()
    print("🎉 Все тесты потокобезопасности прошли успешно!")
