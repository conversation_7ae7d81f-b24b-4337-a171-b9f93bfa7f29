"""Тесты критических исправлений в системе оптимизации."""

import sys
from pathlib import Path
import pytest
import pandas as pd
import numpy as np

# Добавляем корневую директорию проекта в PYTHONPATH
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.coint2.core.normalization_improvements import preprocess_and_normalize_data, compute_normalization_params


class TestCriticalFixes:
    """Тесты критических исправлений."""
    
    def test_zscore_normalization_support(self):
        """Тест поддержки Z-score нормализации."""
        # Создаем тестовые данные
        dates = pd.date_range('2024-01-01', periods=100, freq='15min')
        test_data = pd.DataFrame({
            'BTCUSDT': np.random.normal(50000, 1000, 100),
            'ETHUSDT': np.random.normal(3000, 100, 100),
            'ADAUSDT': np.random.normal(1, 0.1, 100)
        }, index=dates)
        
        # Тестируем расчет параметров для zscore
        params = compute_normalization_params(test_data, norm_method="zscore")
        
        assert 'BTCUSDT' in params, "Параметры должны содержать BTCUSDT"
        assert 'mean' in params['BTCUSDT'], "Параметры должны содержать среднее"
        assert 'std' in params['BTCUSDT'], "Параметры должны содержать стандартное отклонение"
        
        # Тестируем нормализацию
        normalized_data, stats = preprocess_and_normalize_data(
            test_data, 
            norm_method="zscore",
            fill_method="ffill"
        )
        
        assert not normalized_data.empty, "Нормализованные данные не должны быть пустыми"
        assert normalized_data.shape[1] == 3, "Должны остаться все 3 символа"
        
        # Проверяем, что данные действительно нормализованы (среднее ≈ 0, std ≈ 1)
        for col in normalized_data.columns:
            col_mean = normalized_data[col].mean()
            col_std = normalized_data[col].std()
            assert abs(col_mean) < 0.1, f"Среднее для {col} должно быть близко к 0, получено {col_mean}"
            assert abs(col_std - 1) < 0.1, f"Стандартное отклонение для {col} должно быть близко к 1, получено {col_std}"
        
        print("✅ Z-score нормализация работает корректно")
    
    def test_none_unpacking_protection(self):
        """Тест защиты от распаковки None."""
        # Этот тест проверяет, что код не падает при попытке распаковки None
        
        def mock_backtest_function():
            return None  # Симулируем ошибку в бэктесте
        
        # Старый код (проблемный):
        # pair_result, pair_trades = mock_backtest_function()  # Это вызвало бы ошибку
        
        # Новый код (исправленный):
        backtest_output = mock_backtest_function()
        if backtest_output is None:
            # Пропускаем обработку
            result = "skipped"
        else:
            pair_result, pair_trades = backtest_output
            result = "processed"
        
        assert result == "skipped", "Должен корректно обработать None результат"
        print("✅ Защита от распаковки None работает корректно")
    
    def test_normalization_error_handling(self):
        """Тест обработки ошибок нормализации."""
        # Создаем данные с проблемными значениями
        dates = pd.date_range('2024-01-01', periods=10, freq='15min')
        test_data = pd.DataFrame({
            'CONSTANT': [1.0] * 10,  # Константные значения
            'NORMAL': np.random.normal(100, 10, 10)
        }, index=dates)
        
        # Тестируем, что zscore нормализация не падает на константных данных
        try:
            normalized_data, stats = preprocess_and_normalize_data(
                test_data, 
                norm_method="zscore",
                fill_method="ffill"
            )
            
            # Проверяем, что константная колонка обработана корректно
            assert 'CONSTANT' in normalized_data.columns, "Константная колонка должна остаться"
            assert not normalized_data['CONSTANT'].isna().any(), "Не должно быть NaN в константной колонке"
            
            print("✅ Обработка ошибок нормализации работает корректно")
            
        except Exception as e:
            pytest.fail(f"Нормализация не должна падать на константных данных: {e}")

    def test_pnl_calculation_zero_division_protection(self):
        """Тест защиты от деления на ноль в расчетах PnL."""
        print("🧪 Тестирование защиты от деления на ноль в PnL...")

        from src.coint2.engine.optimized_backtest_engine import OptimizedPairBacktester
        import pandas as pd
        import numpy as np

        # Создаем данные с нулевыми ценами
        dates = pd.date_range('2024-01-01', periods=10, freq='15min')
        pair_data = pd.DataFrame({
            'y': [100.0, 101.0, 102.0, 103.0, 0.0, 105.0, 106.0, 107.0, 108.0, 109.0],
            'x': [50.0, 51.0, 52.0, 53.0, 54.0, 0.0, 56.0, 57.0, 58.0, 59.0]
        }, index=dates)

        backtester = OptimizedPairBacktester(
            pair_data=pair_data,
            rolling_window=3,
            z_threshold=1.0,
            z_exit=0.5,
            commission_pct=0.001,
            slippage_pct=0.0005
        )

        # Тестируем прямой вызов метода с нулевыми ценами
        pnl_result = backtester._calculate_position_pnl(
            position=1,
            current_row=pair_data.iloc[6],  # current: y=106, x=56
            previous_row=pair_data.iloc[5]  # previous: y=105, x=0 (ноль!)
        )

        # Результат должен быть конечным числом
        assert np.isfinite(pnl_result), f"PnL должен быть конечным при делении на ноль, получен: {pnl_result}"

        # Тестируем полный запуск
        backtester.run()
        results = backtester.get_results()

        # Проверяем, что результаты корректны
        assert isinstance(results, dict), "Результаты должны быть словарем"
        assert 'pnl' in results, "Результаты должны содержать PnL"

        # Все значения PnL должны быть конечными
        pnl_series = results['pnl']
        finite_count = np.isfinite(pnl_series).sum()
        total_count = len(pnl_series)

        print(f"📊 PnL: {finite_count}/{total_count} конечных значений")
        assert finite_count == total_count, f"Все PnL должны быть конечными, но {total_count - finite_count} значений NaN/inf"

        print("✅ Защита от деления на ноль в PnL работает корректно")


if __name__ == "__main__":
    test = TestCriticalFixes()
    test.test_zscore_normalization_support()
    test.test_none_unpacking_protection()
    test.test_normalization_error_handling()
    test.test_pnl_calculation_zero_division_protection()
    print("🎉 Все тесты критических исправлений прошли успешно!")
